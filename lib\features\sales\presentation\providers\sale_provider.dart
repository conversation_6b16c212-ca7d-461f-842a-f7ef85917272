import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/sale.dart';
import '../../domain/entities/sale_item.dart';
import '../../domain/usecases/create_sale.dart';
import '../../domain/usecases/get_all_sales.dart';
import '../../domain/usecases/get_sale_by_id.dart';
import '../../domain/usecases/update_sale.dart';
import '../../domain/usecases/delete_sale.dart';
import '../../domain/repositories/sale_repository.dart';
import '../../../products/presentation/providers/product_provider.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../customers/domain/entities/customer_account.dart';
import '../../../activities/presentation/providers/activity_provider.dart';

class SaleProvider extends ChangeNotifier {
  final CreateSaleUseCase _createSaleUseCase;
  final GetAllSalesUseCase _getAllSalesUseCase;
  final GetSaleByIdUseCase _getSaleByIdUseCase;
  final UpdateSaleUseCase _updateSaleUseCase;
  final DeleteSaleUseCase _deleteSaleUseCase;
  final SaleRepository _saleRepository;

  SaleProvider()
    : _createSaleUseCase = GetIt.instance<CreateSaleUseCase>(),
      _getAllSalesUseCase = GetIt.instance<GetAllSalesUseCase>(),
      _getSaleByIdUseCase = GetIt.instance<GetSaleByIdUseCase>(),
      _updateSaleUseCase = GetIt.instance<UpdateSaleUseCase>(),
      _deleteSaleUseCase = GetIt.instance<DeleteSaleUseCase>(),
      _saleRepository = GetIt.instance<SaleRepository>();

  // State variables
  List<Sale> _sales = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Filter variables
  String? _selectedPaymentStatus;
  String? _selectedPaymentMethod;
  int? _selectedCustomerId;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;

  // Getters
  List<Sale> get sales => _sales;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get selectedPaymentStatus => _selectedPaymentStatus;
  String? get selectedPaymentMethod => _selectedPaymentMethod;
  int? get selectedCustomerId => _selectedCustomerId;
  DateTime? get selectedFromDate => _selectedFromDate;
  DateTime? get selectedToDate => _selectedToDate;

  // Fetch all sales
  Future<void> fetchSales() async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _getAllSalesUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sales: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create sale with atomic transaction
  Future<bool> createSale(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // Get required providers
      final productProvider = GetIt.instance<ProductProvider>();
      final customerProvider = GetIt.instance<CustomerProvider>();
      final activityProvider = GetIt.instance<ActivityProvider>();

      // Create sale using use case (this handles the database transaction)
      final saleId = await _createSaleUseCase.call(sale, items);

      // Update product quantities and batches for wholesale products
      for (final item in items) {
        if (item.isWholesaleProduct &&
            item.productId != null &&
            item.quantity != null) {
          await productProvider.updateProductQuantitiesForSalesAndPurchases(
            item.productId!,
            item.quantity!,
            isDecrease: true, // Decrease for sale
          );
        }
      }

      // Add customer account entries for registered customers
      if (sale.customerId != null) {
        // تحديد نوع الفاتورة (تجزئة أم جملة)
        final isRetailOnlyInvoice = items.every(
          (item) => item.isRetailGoodsSummary,
        );

        // تحديد نوع الحركة
        String invoiceType = 'sale_invoice';
        String invoiceDescription = 'فاتورة بيع رقم $saleId - إجمالي الفاتورة';

        if (isRetailOnlyInvoice && sale.dueAmount > 0) {
          invoiceType = 'retail_debt';
          invoiceDescription = 'دين تجزئة - فاتورة رقم $saleId';
        }

        // إضافة حركة الفاتورة (كاملة) - دائماً
        await customerProvider.addCustomerAccountEntry(
          CustomerAccount(
            customerId: sale.customerId!,
            transactionDate: sale.saleDate,
            type: invoiceType,
            amount: sale.totalAmount,
            description: invoiceDescription,
            relatedInvoiceId: saleId,
            isPaid: false, // دائماً false لأنها ديون (ستُسدد بسندات قبض)
          ),
        );

        // ملاحظة: لا نضيف حركة دفع منفصلة هنا لتجنب التكرار
        // المدفوعات ستُسجل كسندات قبض منفصلة في جدول payment_receipts
      }

      // Refresh sales list
      await fetchSales();

      // Refresh activities
      await activityProvider.refreshActivities();

      return true;
    } catch (e) {
      _setError('Failed to create sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update sale
  Future<bool> updateSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateSaleUseCase.call(sale);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update sale with items
  Future<bool> updateSaleWithItems(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleRepository.updateSaleWithItems(sale, items);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update sale with items: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete sale
  Future<bool> deleteSale(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _deleteSaleUseCase.call(id);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to delete sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get sale by ID
  Future<Sale?> getSaleById(int id) async {
    try {
      return await _getSaleByIdUseCase.call(id);
    } catch (e) {
      _setError('Failed to get sale: ${e.toString()}');
      return null;
    }
  }

  // Get sale items
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    try {
      return await _saleRepository.getSaleItems(saleId);
    } catch (e) {
      _setError('Failed to get sale items: ${e.toString()}');
      return [];
    }
  }

  // Get sales by customer
  Future<List<Sale>> getSalesByCustomer(int customerId) async {
    try {
      return await _saleRepository.getSalesByCustomer(customerId);
    } catch (e) {
      _setError('Failed to get sales by customer: ${e.toString()}');
      return [];
    }
  }

  // Get sales by status
  List<Sale> getSalesByStatus(String status) {
    return _sales.where((sale) => sale.status == status).toList();
  }

  // Get sales by payment method
  List<Sale> getSalesByPaymentMethod(String paymentMethod) {
    return _sales.where((sale) => sale.paymentMethod == paymentMethod).toList();
  }

  // Get sales statistics
  Future<Map<String, dynamic>> getSalesStatistics() async {
    try {
      return await _saleRepository.getSalesStatistics();
    } catch (e) {
      _setError('Failed to get sales statistics: ${e.toString()}');
      return {};
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear sales list
  void clearSales() {
    _sales.clear();
    notifyListeners();
  }

  // Search sales
  List<Sale> searchSales(String query) {
    if (query.isEmpty) return _sales;

    return _sales.where((sale) {
      final saleId = sale.id?.toString() ?? '';
      final customerId = sale.customerId?.toString() ?? '';
      final searchQuery = query.toLowerCase();

      return saleId.contains(searchQuery) || customerId.contains(searchQuery);
    }).toList();
  }

  // Filter sales
  void filterSales({
    String? paymentStatus,
    String? paymentMethod,
    int? customerId,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    _selectedPaymentStatus = paymentStatus;
    _selectedPaymentMethod = paymentMethod;
    _selectedCustomerId = customerId;
    _selectedFromDate = fromDate;
    _selectedToDate = toDate;
    notifyListeners();
  }

  // Get filtered sales (by all filters and search)
  List<Sale> getFilteredSales(String searchQuery) {
    List<Sale> filteredSales = _sales;

    // Apply payment status filter
    if (_selectedPaymentStatus != null && _selectedPaymentStatus!.isNotEmpty) {
      switch (_selectedPaymentStatus) {
        case 'مدفوعة بالكامل':
          filteredSales = filteredSales
              .where((sale) => sale.isFullyPaid)
              .toList();
          break;
        case 'مدفوعة جزئياً':
          filteredSales = filteredSales
              .where((sale) => sale.isPartiallyPaid)
              .toList();
          break;
        case 'غير مدفوعة':
          filteredSales = filteredSales.where((sale) => sale.isUnpaid).toList();
          break;
      }
    }

    // Apply payment method filter
    if (_selectedPaymentMethod != null && _selectedPaymentMethod!.isNotEmpty) {
      filteredSales = filteredSales
          .where((sale) => sale.paymentMethod == _selectedPaymentMethod)
          .toList();
    }

    // Apply customer filter
    if (_selectedCustomerId != null) {
      filteredSales = filteredSales
          .where((sale) => sale.customerId == _selectedCustomerId)
          .toList();
    }

    // Apply date range filter
    if (_selectedFromDate != null) {
      filteredSales = filteredSales
          .where(
            (sale) =>
                sale.saleDate.isAfter(_selectedFromDate!) ||
                sale.saleDate.isAtSameMomentAs(_selectedFromDate!),
          )
          .toList();
    }
    if (_selectedToDate != null) {
      final endOfDay = DateTime(
        _selectedToDate!.year,
        _selectedToDate!.month,
        _selectedToDate!.day,
        23,
        59,
        59,
      );
      filteredSales = filteredSales
          .where(
            (sale) =>
                sale.saleDate.isBefore(endOfDay) ||
                sale.saleDate.isAtSameMomentAs(endOfDay),
          )
          .toList();
    }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filteredSales = filteredSales.where((sale) {
        final saleId = sale.id?.toString() ?? '';
        final customerId = sale.customerId?.toString() ?? '';
        final searchQueryLower = searchQuery.toLowerCase();

        return saleId.contains(searchQueryLower) ||
            customerId.contains(searchQueryLower);
      }).toList();
    }

    return filteredSales;
  }

  // Clear all filters
  void clearFilters() {
    _selectedPaymentStatus = null;
    _selectedPaymentMethod = null;
    _selectedCustomerId = null;
    _selectedFromDate = null;
    _selectedToDate = null;
    notifyListeners();
  }
}
