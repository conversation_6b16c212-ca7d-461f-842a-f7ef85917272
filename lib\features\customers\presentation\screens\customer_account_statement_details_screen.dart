import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/customers/domain/entities/customer.dart';
import 'package:market/features/customers/domain/entities/customer_account.dart';
import 'package:market/features/customers/presentation/providers/customer_provider.dart';
import 'package:market/features/transactions/presentation/providers/payment_receipt_provider.dart';
import 'package:market/features/sales/presentation/providers/sale_provider.dart';

class CustomerAccountStatementDetailsScreen extends StatefulWidget {
  final String
  customerId; // يمكن أن تكون String أو int حسب المسار في app_router

  const CustomerAccountStatementDetailsScreen({
    super.key,
    required this.customerId,
  });

  @override
  State<CustomerAccountStatementDetailsScreen> createState() =>
      _CustomerAccountStatementDetailsScreenState();
}

class _CustomerAccountStatementDetailsScreenState
    extends State<CustomerAccountStatementDetailsScreen> {
  Customer? _customer;
  List<CustomerAccount> _transactions = [];
  double _currentBalance = 0.0;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadCustomerStatement();
  }

  Future<void> _loadCustomerStatement() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final customerProvider = context.read<CustomerProvider>();
      final paymentReceiptProvider = context.read<PaymentReceiptProvider>();
      final salesProvider = context.read<SaleProvider>();
      final int customerIdInt = int.parse(widget.customerId);

      _customer = await customerProvider.getCustomerById(customerIdInt);

      // 1. جلب فواتير البيع المرتبطة بهذا العميل
      final sales = await salesProvider.getSalesByCustomer(customerIdInt);

      // تحويل Sales إلى قائمة موحدة للعرض (تمثل ديون العميل)
      final List<CustomerAccount> combinedTransactions = [];

      for (final sale in sales) {
        // أضف الفاتورة كدين إذا كان هناك مبلغ إجمالي
        if (sale.totalAmount > 0) {
          // أضف الفاتورة دائماً، والرصيد سيتعامل مع المدفوع
          combinedTransactions.add(
            CustomerAccount(
              // استخدام CustomerAccount كـ "بند كشف حساب" للعرض الموحد
              id: sale.id ?? 0,
              customerId: customerIdInt,
              amount: sale.totalAmount, // المبلغ الإجمالي للفاتورة
              type: sale.paymentMethod == 'credit'
                  ? 'retail_debt'
                  : 'sale_invoice', // يمكن التمييز كدين أو فاتورة آجلة
              description: 'فاتورة بيع رقم ${sale.id}',
              transactionDate: sale.saleDate,
              relatedInvoiceId: sale.id,
              isPaid: sale.isFullyPaid,
            ),
          );
        }
      }

      // 2. جلب سندات القبض المرتبطة بهذا العميل (payment_in)
      final paymentReceipts = await paymentReceiptProvider
          .getPaymentReceiptsByEntity(
            entityId: customerIdInt,
            entityType: 'customer',
          );

      // تحويل السندات إلى قائمة موحدة للعرض (تمثل مدفوعات العميل)
      for (final receipt in paymentReceipts) {
        if (receipt.type == 'payment_in') {
          // تأكد أنه سند قبض
          combinedTransactions.add(
            CustomerAccount(
              id: receipt.id ?? 0,
              customerId: customerIdInt,
              amount: receipt.amount, // المبلغ الأصلي للسند (موجب)
              type: 'payment_in', // النوع الحقيقي للسند
              description: receipt.description ?? 'سند قبض رقم ${receipt.id}',
              transactionDate: receipt.transactionDate,
              relatedInvoiceId: receipt.relatedInvoiceId,
              isPaid: true, // السندات تُعتبر مدفوعة
            ),
          );
        }
      }

      // دمج القوائم وترتيبها حسب التاريخ (من الأحدث للأقدم)
      _transactions = combinedTransactions;
      _transactions.sort(
        (a, b) => b.transactionDate.compareTo(a.transactionDate),
      );

      // تحديث _currentBalance مباشرة من حقل العميل المحدث
      // هذا يفترض أن customer.currentBalance تم تحديثه بالفعل في CustomerProvider
      // بعد إضافة السند (كما تم في المهمة 1.4)
      _currentBalance = _customer!.currentBalance;

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'فشل في تحميل كشف الحساب: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: _customer?.name ?? 'كشف حساب العميل',
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          // الانتقال إلى شاشة إضافة سند مع تمرير معلومات العميل
          final result = await context.push(
            '/transactions/receipts/new',
            extra: {
              'relatedEntityType': 'customer',
              'relatedEntityId': int.parse(widget.customerId),
            },
          );

          // إعادة تحميل البيانات إذا تم إنشاء سند بنجاح
          if (result == true) {
            await _loadCustomerStatement();
          }
        },
        tooltip: 'إضافة سند قبض',
        child: const Icon(Icons.add),
      ),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadCustomerStatement,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            )
          : Column(
              children: [
                _buildCustomerHeader(), // معلومات العميل والرصيد
                const Divider(),
                Expanded(child: _buildTransactionsList()), // قائمة المعاملات
              ],
            ),
    );
  }

  Widget _buildCustomerHeader() {
    final balanceColor = _currentBalance > 0
        ? Colors.red
        : (_currentBalance < 0 ? Colors.green : Colors.grey);
    final balanceText = _currentBalance > 0
        ? 'مدين بـ ${_currentBalance.toStringAsFixed(2)} ر.ي'
        : (_currentBalance < 0
              ? 'دائن بـ ${(-_currentBalance).toStringAsFixed(2)} ر.ي'
              : 'رصيد صفر');

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 28,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Text(
                    _customer?.name.isNotEmpty == true
                        ? _customer!.name[0].toUpperCase()
                        : 'ع',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _customer?.name ?? 'عميل غير معروف',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            Text(
              'الرصيد الحالي:',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Text(
              balanceText,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: balanceColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            if (_customer?.phone != null && _customer!.phone!.isNotEmpty)
              Text(
                'رقم الهاتف: ${_customer!.phone}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList() {
    if (_transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد حركات حساب لهذا العميل',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction =
            _transactions[index]; // هذا سيكون من نوع CustomerAccount

        final bool isDebit;
        String transactionDescription;
        IconData transactionIcon;
        Color iconColor;

        if (transaction.type == 'sale_invoice' ||
            transaction.type == 'retail_debt') {
          isDebit = true;
          transactionDescription = transaction.description ?? 'فاتورة بيع';
          transactionIcon = Icons.arrow_circle_up; // يشير إلى زيادة الدين
          iconColor = Colors.red;
        } else if (transaction.type == 'payment_in') {
          isDebit = false;
          transactionDescription = transaction.description ?? 'سند قبض';
          transactionIcon = Icons.arrow_circle_down; // يشير إلى تخفيض الدين
          iconColor = Colors.green;
        } else {
          isDebit = false; // افتراضي
          transactionDescription =
              transaction.description ?? 'معاملة غير معرفة';
          transactionIcon = Icons.info;
          iconColor = Colors.grey;
        }

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          elevation: 1,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: iconColor.withValues(alpha: 0.1),
              child: Icon(transactionIcon, color: iconColor),
            ),
            title: Text(
              transactionDescription,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              DateFormat(
                'yyyy-MM-dd HH:mm',
              ).format(transaction.transactionDate),
            ),
            trailing: Text(
              '${isDebit ? '+' : '-'} ${transaction.amount.abs().toStringAsFixed(2)} ر.ي',
              style: TextStyle(
                color: iconColor,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            // يمكن إضافة onTap هنا للانتقال لتفاصيل الفاتورة/السند
          ),
        );
      },
    );
  }
}
